# MQTT WebSocket Outbound 插件

这是一个支持通过WebSocket连接到MQTT broker的outbound插件。该插件允许不同的publish topic代表不同的action，每个topic支持配置QoS级别。

## 特性

- ✅ 支持WebSocket连接到MQTT broker
- ✅ 不同action对应不同的topic和QoS配置
- ✅ 自动重连机制
- ✅ 状态监控和上报
- ✅ 支持MQTT认证（用户名/密码）
- ✅ 可配置的保活间隔和重连参数
- ✅ 支持SSL/TLS连接（wss://）

## 安装依赖

```bash
poetry add paho-mqtt
```

## 配置说明

### 基本配置

```yaml
outbounds:
  mqtt_ws_output:
    type: "mqtt_websocket"
    id: "mqtt_ws_01"
    broker_url: "ws://localhost:9001/mqtt"  # MQTT WebSocket broker地址
    client_id: "beezer_client_001"  # 可选，如果不提供会自动生成
    username: null  # MQTT用户名，如果不需要认证可以设为null
    password: null  # MQTT密码
    keep_alive: 60  # 保活间隔（秒）
    reconnect_interval: 5  # 重连间隔（秒）
    max_reconnect_attempts: 10  # 最大重连次数
    default_qos: 0  # 默认QoS级别
    topics:
      # 不同的action对应不同的topic和QoS配置
      sensor_data:
        topic: "sensors/data"
        qos: 1
      alarm_data:
        topic: "alarms/critical"
        qos: 2
      status_update:
        topic: "status/machine"
        qos: 0
```

### 配置参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `type` | string | ✅ | - | 必须为 "mqtt_websocket" |
| `id` | string | ✅ | - | 插件唯一标识符 |
| `broker_url` | string | ✅ | - | MQTT WebSocket broker URL |
| `client_id` | string | ❌ | 自动生成 | MQTT客户端ID |
| `username` | string | ❌ | null | MQTT用户名 |
| `password` | string | ❌ | null | MQTT密码 |
| `keep_alive` | int | ❌ | 60 | 保活间隔（秒） |
| `reconnect_interval` | int | ❌ | 5 | 重连间隔（秒） |
| `max_reconnect_attempts` | int | ❌ | 10 | 最大重连次数 |
| `default_qos` | int | ❌ | 0 | 默认QoS级别 (0, 1, 2) |
| `topics` | dict | ❌ | {} | action到topic配置的映射 |

### Topic配置

每个topic配置包含：

```yaml
action_name:
  topic: "mqtt/topic/path"  # MQTT主题路径
  qos: 1  # QoS级别 (0, 1, 2)
```

## 支持的Action

### 自动注册的Action

插件会根据`topics`配置自动注册对应的action。例如，如果配置了：

```yaml
topics:
  sensor_data:
    topic: "sensors/data"
    qos: 1
```

则会自动注册一个名为`sensor_data`的action。

### 内置Action

- `publish`: 通用发布action，需要在config中指定topic和qos
- `send`: 与publish相同的功能

## 使用示例

### 在Flow中使用

```yaml
flows:
  - name: "mqtt_sensor_flow"
    inbounds: ["sensor_input"]
    outbound: "mqtt_ws_output"
    rules:
      - name: "sensor_to_mqtt"
        trigger: ["sensor_input"]
        action: "sensor_data"  # 使用预配置的action
```

### 直接调用

```python
# 使用预配置的action
result = await plugin.execute(data, "sensor_data")

# 使用通用publish action
result = await plugin.execute(
    data, 
    "publish",
    {"topic": "custom/topic", "qos": 1}
)
```

## MQTT Broker设置

### 使用Mosquitto

1. 安装Mosquitto：
```bash
# Ubuntu/Debian
sudo apt-get install mosquitto mosquitto-clients

# macOS
brew install mosquitto
```

2. 配置WebSocket支持（mosquitto.conf）：
```
port 1883
listener 9001
protocol websockets
allow_anonymous true
```

3. 启动broker：
```bash
mosquitto -c mosquitto.conf
```

### 使用Docker

```bash
# 创建配置文件
cat > mosquitto.conf << EOF
port 1883
listener 9001
protocol websockets
allow_anonymous true
EOF

# 运行容器
docker run -it -p 1883:1883 -p 9001:9001 \
  -v $(pwd)/mosquitto.conf:/mosquitto/config/mosquitto.conf \
  eclipse-mosquitto:latest
```

## 测试

运行测试脚本：

```bash
python test_mqtt_websocket.py
```

## 状态监控

插件会定期上报状态到监控系统，包括：

- 连接状态
- 发布计数
- 错误信息
- 重连尝试次数

## 错误处理

- 自动重连：连接断开时会自动尝试重连
- 重试机制：发布失败时会记录错误并继续运行
- 状态上报：所有错误都会上报到监控系统

## QoS级别说明

- **QoS 0**: 最多一次传递，不保证消息到达
- **QoS 1**: 至少一次传递，保证消息到达但可能重复
- **QoS 2**: 恰好一次传递，保证消息到达且不重复

根据数据的重要性选择合适的QoS级别：
- 传感器数据：通常使用QoS 1
- 告警数据：建议使用QoS 2
- 状态更新：可以使用QoS 0

## 故障排除

### 常见问题

1. **连接失败**
   - 检查broker_url是否正确
   - 确认MQTT broker支持WebSocket
   - 检查网络连接

2. **认证失败**
   - 验证用户名和密码
   - 检查broker的认证配置

3. **发布失败**
   - 检查topic权限
   - 验证QoS级别支持
   - 查看broker日志

### 调试

启用调试日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 许可证

本插件遵循项目的许可证协议。
