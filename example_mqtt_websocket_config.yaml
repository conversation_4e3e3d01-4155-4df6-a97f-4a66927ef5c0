version: 1

# 应用配置
apps:
  - name: "mqtt_websocket_demo"
    type: "basic_flow"
    args: []

# 输入源配置
inbounds:
  mock_sensor:
    type: "mock"
    id: "mock_sensor_01"
    interval: 5  # 每5秒生成一次数据
    data_type: "sensor"  # 传感器数据类型
    error_rate: 0.1  # 10%的错误率

# 输出配置
outbounds:
  mqtt_ws_output:
    type: "mqtt_websocket"
    id: "mqtt_ws_01"
    broker_url: "ws://localhost:9001/mqtt"  # MQTT WebSocket broker地址
    client_id: "beezer_client_001"  # 可选，如果不提供会自动生成
    username: null  # MQTT用户名，如果不需要认证可以设为null
    password: null  # MQTT密码
    keep_alive: 60  # 保活间隔（秒）
    reconnect_interval: 5  # 重连间隔（秒）
    max_reconnect_attempts: 10  # 最大重连次数
    default_qos: 0  # 默认QoS级别
    topics:
      # 不同的action对应不同的topic和QoS配置
      sensor_data:
        topic: "sensors/data"
        qos: 1
      alarm_data:
        topic: "alarms/critical"
        qos: 2
      status_update:
        topic: "status/machine"
        qos: 0
      heartbeat:
        topic: "system/heartbeat"
        qos: 0

# 规则配置
rules:
  - name: "sensor_data_rule"
    rules:
      - source: "$.temperature"
        target: "$.sensor_temp"
        type: "reference"
      - source: "$.humidity"
        target: "$.sensor_humidity"
        type: "reference"
      - source: "$.timestamp"
        target: "$.data_timestamp"
        type: "reference"
    trigger:
      mode: "change"
      type: "any"

# 流程配置
flows:
  - name: "mqtt_sensor_flow"
    inbounds: ["mock_sensor"]
    outbound: "mqtt_ws_output"
    rules:
      - name: "sensor_to_mqtt"
        trigger: ["mock_sensor"]
        action: "sensor_data"  # 使用sensor_data action，会发布到sensors/data topic，QoS=1

# 服务器端口
server_port: 9999
