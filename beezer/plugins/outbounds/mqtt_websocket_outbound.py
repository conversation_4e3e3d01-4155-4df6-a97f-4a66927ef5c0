"""
MQTT WebSocket Outbound Plugin

支持通过WebSocket连接到MQTT broker的outbound插件。
不同的publish topic代表不同的action，每个topic支持配置QoS。
"""

import json
import time
import uuid
import asyncio
from typing import Dict, Any
from loguru import logger

import paho.mqtt.client as mqtt
from paho.mqtt.enums import CallbackAPIVersion

from beezer.plugins._base import OutboundPlugin
from beezer.type_model import MqttWebSocketOutboundConfig


class MqttWebSocketOutbound(OutboundPlugin):
    """MQTT WebSocket 数据输出插件。"""

    plugin_protocol = "mqtt_websocket"

    def __init__(self, config: Dict[str, Any]):
        validated_config = MqttWebSocketOutboundConfig.model_validate(config)
        super().__init__(validated_config)
        self.config = validated_config
        self._client = None
        self._connected = False
        self._reconnect_attempts = 0
        self._publish_counter = 0
        self._connection_lock = asyncio.Lock()

        # 生成客户端ID（如果未提供）
        if not self.config.client_id:
            self.config.client_id = f"beezer_mqtt_{uuid.uuid4().hex[:8]}"

        # 注册动作 - 基于配置的topics创建动作
        self._register_topic_actions()

        # 注册默认动作
        self.register_action("publish", self.action_publish)
        self.register_action("send", self.action_send)

        logger.info(f"MQTT WebSocket Outbound {self.config.id} 已初始化")

    def _register_topic_actions(self):
        """根据配置的topics注册对应的actions"""
        for action_name, topic_config in self.config.topics.items():
            # 为每个topic创建一个专门的action
            action_func = self._create_topic_action(action_name, topic_config)
            self.register_action(action_name, action_func)
            logger.debug(
                f"注册action: {action_name} -> topic: {topic_config.topic}, qos: {topic_config.qos}"
            )

    def _create_topic_action(self, action_name: str, topic_config):
        """为特定topic创建action函数"""

        async def topic_action(data: Any, config: Dict[str, Any]) -> Any:
            _ = config  # 忽略未使用的参数
            return await self._publish_to_topic(
                data, topic_config.topic, topic_config.qos, action_name
            )

        return topic_action

    async def _ensure_connected(self):
        """确保MQTT连接已建立"""
        async with self._connection_lock:
            if not self._connected:
                await self._connect()

    async def _connect(self):
        """建立MQTT WebSocket连接"""
        try:
            # 创建MQTT客户端
            self._client = mqtt.Client(
                callback_api_version=CallbackAPIVersion.VERSION2,
                client_id=self.config.client_id,
                transport="websockets",
            )

            # 设置回调函数
            self._client.on_connect = self._on_connect
            self._client.on_disconnect = self._on_disconnect
            self._client.on_publish = self._on_publish

            # 设置认证信息
            if self.config.username and self.config.password:
                self._client.username_pw_set(self.config.username, self.config.password)

            # 解析WebSocket URL
            broker_url = self.config.broker_url
            if broker_url.startswith("ws://"):
                host = broker_url[5:].split("/")[0].split(":")[0]
                port_path = broker_url[5:].split("/", 1)
                if ":" in port_path[0]:
                    port = int(port_path[0].split(":")[1])
                else:
                    port = 80
                path = "/" + port_path[1] if len(port_path) > 1 else "/"
            elif broker_url.startswith("wss://"):
                host = broker_url[6:].split("/")[0].split(":")[0]
                port_path = broker_url[6:].split("/", 1)
                if ":" in port_path[0]:
                    port = int(port_path[0].split(":")[1])
                else:
                    port = 443
                path = "/" + port_path[1] if len(port_path) > 1 else "/"
                self._client.tls_set()
            else:
                raise ValueError(f"不支持的broker URL格式: {broker_url}")

            # 设置WebSocket路径
            self._client.ws_set_options(path=path)

            # 连接到broker
            logger.info(f"正在连接到MQTT broker: {host}:{port}{path}")
            self._client.connect_async(host, port, self.config.keep_alive)
            self._client.loop_start()

            # 等待连接建立
            for _ in range(50):  # 最多等待5秒
                if self._connected:
                    break
                await asyncio.sleep(0.1)

            if not self._connected:
                raise Exception("MQTT连接超时")

            self._reconnect_attempts = 0
            logger.info(f"MQTT WebSocket连接已建立: {broker_url}")

            await self.report_status(
                "connected",
                {
                    "broker_url": self.config.broker_url,
                    "client_id": self.config.client_id,
                    "reconnect_attempts": self._reconnect_attempts,
                },
            )

        except Exception as e:
            logger.error(f"MQTT WebSocket连接失败: {e}")
            await self.report_status(
                "error",
                {
                    "error_message": str(e),
                    "broker_url": self.config.broker_url,
                    "reconnect_attempts": self._reconnect_attempts,
                },
            )
            raise

    def _on_connect(self, client, userdata, flags, reason_code, properties):
        """MQTT连接回调"""
        _ = client, userdata, flags, properties  # 忽略未使用的参数
        if reason_code == 0:
            self._connected = True
            logger.info(f"MQTT客户端已连接: {self.config.client_id}")
        else:
            self._connected = False
            logger.error(f"MQTT连接失败，错误码: {reason_code}")

    def _on_disconnect(self, client, userdata, flags, reason_code, properties):
        """MQTT断开连接回调"""
        _ = client, userdata, flags, properties  # 忽略未使用的参数
        self._connected = False
        logger.warning(f"MQTT客户端已断开连接，错误码: {reason_code}")

    def _on_publish(self, client, userdata, mid, reason_code, properties):
        """MQTT发布回调"""
        _ = client, userdata, properties  # 忽略未使用的参数
        if reason_code == 0:
            logger.debug(f"消息发布成功，mid: {mid}")
        else:
            logger.warning(f"消息发布失败，mid: {mid}, 错误码: {reason_code}")

    async def _reconnect(self):
        """重新连接"""
        if self._reconnect_attempts >= self.config.max_reconnect_attempts:
            logger.error(
                f"达到最大重连次数 {self.config.max_reconnect_attempts}，停止重连"
            )
            return

        self._reconnect_attempts += 1
        logger.info(
            f"尝试重连 ({self._reconnect_attempts}/{self.config.max_reconnect_attempts})"
        )

        try:
            await asyncio.sleep(self.config.reconnect_interval)
            await self._connect()
        except Exception as e:
            logger.error(f"重连失败: {e}")
            # 继续尝试重连
            asyncio.create_task(self._reconnect())

    async def _publish_to_topic(
        self, data: Any, topic: str, qos: int, action_name: str
    ) -> Any:
        """发布数据到指定topic"""
        try:
            await self.report_status(
                "running",
                {
                    "action": action_name,
                    "topic": topic,
                    "qos": qos,
                    "publish_count": self._publish_counter,
                    "last_publish_time": time.strftime("%H:%M:%S"),
                },
            )

            await self._ensure_connected()

            # 格式化数据为JSON
            if isinstance(data, (dict, list)):
                message = json.dumps(data, ensure_ascii=False)
            else:
                message = str(data)

            # 发布消息
            result = self._client.publish(topic, message, qos=qos)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self._publish_counter += 1
                logger.debug(f"消息已发布到 {topic} (QoS {qos}): {len(message)} 字符")

                # 定期上报状态
                if self._publish_counter % 10 == 0:
                    await self.report_status(
                        "running",
                        {
                            "total_publishes": self._publish_counter,
                            "broker_url": self.config.broker_url,
                            "last_action": action_name,
                            "last_topic": topic,
                        },
                    )

                return {
                    "success": True,
                    "publish_count": self._publish_counter,
                    "topic": topic,
                    "qos": qos,
                    "message_id": result.mid,
                }
            else:
                raise Exception(f"发布失败，错误码: {result.rc}")

        except Exception as e:
            await self.report_status(
                "error",
                {
                    "error_message": str(e),
                    "action": action_name,
                    "topic": topic,
                },
            )
            logger.error(f"MQTT发布失败: {e}")

            # 如果连接断开，尝试重连
            if not self._connected:
                asyncio.create_task(self._reconnect())

            return {"success": False, "error": str(e)}

    async def write(self, data: Any, config: Dict[str, Any]) -> Any:
        """默认写入实现"""
        return await self.action_publish(data, config)

    async def action_publish(self, data: Any, config: Dict[str, Any]) -> Any:
        """通用发布动作"""
        # 从config中获取topic和qos，如果没有则使用默认值
        topic = config.get("topic", "default")
        qos = config.get("qos", self.config.default_qos)

        return await self._publish_to_topic(data, topic, qos, "publish")

    async def action_send(self, data: Any, config: Dict[str, Any]) -> Any:
        """发送动作（与publish相同）"""
        return await self.action_publish(data, config)

    async def close(self):
        """关闭MQTT连接"""
        if self._client and self._connected:
            self._client.loop_stop()
            self._client.disconnect()
            self._connected = False
            logger.info("MQTT WebSocket连接已关闭")

            await self.report_status(
                "disconnected",
                {
                    "reason": "manual_close",
                    "total_publishes": self._publish_counter,
                },
            )
