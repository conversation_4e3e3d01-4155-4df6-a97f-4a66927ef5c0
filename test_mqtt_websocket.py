#!/usr/bin/env python3
"""
测试MQTT WebSocket Outbound插件的脚本

这个脚本会：
1. 创建一个MQTT WebSocket outbound插件实例
2. 测试不同的action和topic配置
3. 验证QoS设置是否正确
"""

import asyncio
import json
from beezer.plugins.outbounds.mqtt_websocket_outbound import MqttWebSocketOutbound


async def test_mqtt_websocket_outbound():
    """测试MQTT WebSocket outbound插件"""
    
    # 配置示例
    config = {
        "type": "mqtt_websocket",
        "id": "test_mqtt_ws",
        "broker_url": "ws://localhost:9001/mqtt",  # 需要一个支持WebSocket的MQTT broker
        "client_id": "test_client_001",
        "username": None,
        "password": None,
        "keep_alive": 60,
        "reconnect_interval": 5,
        "max_reconnect_attempts": 3,
        "default_qos": 0,
        "topics": {
            "sensor_data": {
                "topic": "test/sensors/data",
                "qos": 1
            },
            "alarm_data": {
                "topic": "test/alarms/critical", 
                "qos": 2
            },
            "status_update": {
                "topic": "test/status/machine",
                "qos": 0
            }
        }
    }
    
    try:
        # 创建插件实例
        print("创建MQTT WebSocket outbound插件...")
        plugin = MqttWebSocketOutbound(config)
        
        # 测试数据
        sensor_data = {
            "timestamp": "2024-01-01T12:00:00Z",
            "temperature": 25.5,
            "humidity": 60.2,
            "device_id": "sensor_001"
        }
        
        alarm_data = {
            "timestamp": "2024-01-01T12:01:00Z",
            "level": "critical",
            "message": "Temperature too high",
            "device_id": "sensor_001"
        }
        
        status_data = {
            "timestamp": "2024-01-01T12:02:00Z",
            "status": "running",
            "uptime": 3600,
            "device_id": "machine_001"
        }
        
        print("测试不同的action...")
        
        # 测试sensor_data action (QoS 1)
        print("1. 测试sensor_data action...")
        result1 = await plugin.execute(sensor_data, "sensor_data")
        print(f"   结果: {result1}")
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 测试alarm_data action (QoS 2)
        print("2. 测试alarm_data action...")
        result2 = await plugin.execute(alarm_data, "alarm_data")
        print(f"   结果: {result2}")
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 测试status_update action (QoS 0)
        print("3. 测试status_update action...")
        result3 = await plugin.execute(status_data, "status_update")
        print(f"   结果: {result3}")
        
        # 等待一下
        await asyncio.sleep(1)
        
        # 测试通用publish action
        print("4. 测试通用publish action...")
        result4 = await plugin.execute(
            {"test": "generic publish"}, 
            "publish"
        )
        print(f"   结果: {result4}")
        
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        if 'plugin' in locals():
            await plugin.close()


def print_usage():
    """打印使用说明"""
    print("""
MQTT WebSocket Outbound 插件测试脚本

使用前请确保：
1. 有一个支持WebSocket的MQTT broker运行在 ws://localhost:9001/mqtt
   
   推荐使用Mosquitto broker，配置WebSocket支持：
   
   # mosquitto.conf
   port 1883
   listener 9001
   protocol websockets
   
   启动命令：
   mosquitto -c mosquitto.conf

2. 或者使用Docker运行：
   docker run -it -p 1883:1883 -p 9001:9001 eclipse-mosquitto:latest

3. 安装了paho-mqtt依赖：
   poetry add paho-mqtt

运行测试：
   python test_mqtt_websocket.py
""")


if __name__ == "__main__":
    print("MQTT WebSocket Outbound 插件测试")
    print("=" * 50)
    
    # 检查是否有帮助参数
    import sys
    if len(sys.argv) > 1 and sys.argv[1] in ["-h", "--help", "help"]:
        print_usage()
        sys.exit(0)
    
    # 运行测试
    asyncio.run(test_mqtt_websocket_outbound())
